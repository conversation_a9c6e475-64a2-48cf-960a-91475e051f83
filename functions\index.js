const { onCall, onRequest } = require('firebase-functions/v2/https');
const { initializeApp } = require('firebase-admin/app');

// Initialize Firebase Admin
initializeApp();

// Main API endpoint - Firebase callable functions handle CORS automatically
exports.api = onCall({ region: 'australia-southeast1' }, (request) => {
  const data = request.data || {};
  const endpoint = data.endpoint || 'health';

  switch (endpoint) {
    case 'health':
      return {
        status: 'success',
        message: 'API working',
        region: 'australia-southeast1'
      };
    
    case 'execute_prompt':
      return {
        status: 'success',
        message: 'Mock execution',
        region: 'australia-southeast1',
        response: 'Test response from Australia'
      };
    
    case 'test_openrouter_connection':
      return {
        status: 'success',
        message: 'Mock connection',
        region: 'australia-southeast1'
      };
    
    default:
      return {
        status: 'error',
        message: `Unknown endpoint: ${endpoint}`
      };
  }
});

// Health check endpoint
exports.health = onRequest({ region: 'australia-southeast1' }, (req, res) => {
  res.json({
    status: 'healthy',
    region: 'australia-southeast1'
  });
});
